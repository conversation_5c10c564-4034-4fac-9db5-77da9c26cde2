package pieces; //Queen belongs to package pieces.
import java.util.ArrayList;
import java.util.List;

/**
 * Represents a Queen piece in a chess game.
 * The Queen is the most powerful piece, able to move in any direction—horizontally,
 * vertically, or diagonally—any number of squares.
 */



public class Queen extends Piece {

    /** The character representing this piece type. Always 'Q' for Queen. */
    private char name = 'Q';

    /** The current position of the Queen on the board. */
   // public Position position = new Position();

    /**
     * Constructs a new Queen with the specified color and position.
     *
     * @param color The color of the queen ('w' or 'b').
     * @param r     The row coordinate of the queen's starting position.
     * @param c     The column coordinate of the queen's starting position.
     */
    public Queen(char color, int r, int c) {
        //position.setPosition(r, c);
        position.setPosition(r, c);
        this.color = color;
    }

    /**
     * Returns the full string representation of the piece (e.g., "wQ" or "bQ").
     *
     * @return A string combining color and piece name.
     */
    public String getPiece() {
        return String.valueOf(color) + String.valueOf(name);
    }

    @Override
    public List<Position> possibleMoves(Piece[][] board) {
        List<Position> moves = new ArrayList<>();
        int row = position.getRow();
        int col = position.getCollumn();
        
        // Queen moves in 8 directions
        int[][] directions = {
            {-1, -1}, {-1, 0}, {-1, 1},  // top-left, top, top-right
            {0, -1}, {0, 1},             // left, right
            {1, -1}, {1, 0}, {1, 1}      // bottom-left, bottom, bottom-right
        };
        
        for (int[] dir : directions) {
            int newRow = row + dir[0];
            int newCol = col + dir[1];
            
            while (newRow >= 0 && newRow < 8 && newCol >= 0 && newCol < 8) {
                Piece targetPiece = board[newRow][newCol];
                if (targetPiece == null) {
                    Position pos = new Position();
                    pos.setPosition(newRow, newCol);
                    moves.add(pos);
                } else {
                    if (targetPiece.getColor() != color) {
                        Position pos = new Position();
                        pos.setPosition(newRow, newCol);
                        moves.add(pos);
                    }
                    break; // Stop at first piece
                }
                newRow += dir[0];
                newCol += dir[1];
            }
        }
        
        return moves;
    }
}
