/**
 * Represents a position on a board with a row and a column.
 * Provides methods to set and retrieve the position coordinates.
 */

package utils; //Position belongs to package utils.

public class Position {
    /**
     * The column index of the position.
     */
    private int column;

    /**
     * The row index of the position.
     */
    private int row;

    /**
     * Default constructor that creates a Position object with default coordinates (0,0).
     */
    public Position() {}

    /**
     * Sets the position coordinates.
     *
     * @param r The row index to set.
     * @param c The column index to set.
     */
    public void setPosition(int r, int c) {
        column = c;
        row = r;
    }

    /**
     * Returns the current column index of the position.
     *
     * @return The column index.
     */
    public int getColumn() {
        return column;
    }

    /**
     * Returns the current row index of the position.
     *
     * @return The row index.
     */
    public int getRow() {
        return row;
    }
}
