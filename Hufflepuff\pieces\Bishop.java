package pieces; //<PERSON> belongs to package pieces.
import java.util.ArrayList;
import java.util.List;

/**
 * Represents a <PERSON> piece in a chess game.
 * A <PERSON> moves diagonally across the board and can be either white or black.
 */


public class Bishop extends Piece{

    /** The character representing this piece type. Always 'B' for <PERSON>. */
    private char name = 'B';

    /** The current position of the Bishop on the board. */
   // public Position position = new Position();

    /**
     * Constructs a new <PERSON> with the specified color and position.
     *
     * @param color The color of the bishop ('w' or 'b').
     * @param r     The row coordinate of the bishop's starting position.
     * @param c     The column coordinate of the bishop's starting position.
     */
    public Bishop(char color, int r, int c) {
        //position.setPosition(r, c);
        position.setPosition(r, c);
        this.color = color;
    }

    /**
     * Returns the full string representation of the piece (e.g., "wB" or "bB").
     *
     * @return A string combining color and piece name.
     */
    public String getPiece() {
        return String.valueOf(color) + String.valueOf(name);
    }

    @Override
    public List<Position> possibleMoves(Piece[][] board) {
        List<Position> moves = new ArrayList<>();
        int row = position.getRow();
        int col = position.getCollumn();
        
        // top-left, top-right, bottom-left, bottom-right
        int[][] directions = {{-1, -1}, {-1, 1}, {1, -1}, {1, 1}};
        
        for (int[] dir : directions) {
            int newRow = row + dir[0];
            int newCol = col + dir[1];
            
            while (newRow >= 0 && newRow < 8 && newCol >= 0 && newCol < 8) {
                Piece targetPiece = board[newRow][newCol];
                if (targetPiece == null) {
                    Position pos = new Position();
                    pos.setPosition(newRow, newCol);
                    moves.add(pos);
                } else {
                    if (targetPiece.getColor() != color) {
                        Position pos = new Position();
                        pos.setPosition(newRow, newCol);
                        moves.add(pos);
                    }
                    break; // Stop if there's a piece of the same color or a piece of the opposite color
                }
                newRow += dir[0];
                newCol += dir[1];
            }
        }
        
        return moves;
    }
}
