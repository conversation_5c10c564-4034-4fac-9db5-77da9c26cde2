package pieces;

public class Player {
    private char color;
    public Pawn[] myPawns = new Pawn[8];
    public Rook[] myRooks = new Rook[2];
    public Knight[] myKnights = new Knight[2];
    public Bishop[] myBish<PERSON> = new Bishop[2];
    public Queen myQueen;
    public King myKing;

    public Player(char p) {
        color = p;
        if(p == 'b'){
            myQueen = new <PERSON>(color, 7, 3);
            my<PERSON><PERSON> = new <PERSON>(color, 7, 4);
        } else {
            myQueen = new <PERSON>(color, 0, 3);
            myKing = new King(color, 0, 4);
        }
        
        for(int i = 0; i < 8; i++){
            if(p == 'b'){
                myPawns[i] = new Pawn(color, 6, i);
            } else {
                myPawns[i] = new Pawn(color, 1, i);
            }
        }

        myRooks[0] = new Rook(color, p == 'b' ? 7 : 0, 0);
        myRooks[1] = new Rook(color, p == 'b' ? 7 : 0, 7);

        myKnights[0] = new Knight(color, p == 'b' ? 7 : 0, 1);
        myKnights[1] = new <PERSON>(color, p == 'b' ? 7 : 0, 6);

        myBish<PERSON>[0] = new <PERSON>(color, p == 'b' ? 7 : 0, 2);
        myBishops[1] = new Bishop(color, p == 'b' ? 7 : 0, 5);
    }
}