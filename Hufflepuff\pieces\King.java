package pieces; //King belongs to package pieces.
import java.util.ArrayList;
import java.util.List;

/**
 * Represents a King piece in a chess game.
 * The King is a vital piece that can move one square in any direction.
 * The game ends if the King is checkmated.
 */

public class King extends Piece {

    /** The character representing this piece type. Always 'K' for King. */
    private char name = '<PERSON>';

    // /** The current position of the King on the board. */
    // public Position position = new Position();

    /**
     * Constructs a new King with the specified color and position.
     *
     * @param color The color of the king ('w' or 'b').
     * @param r     The row coordinate of the king's starting position.
     * @param c     The column coordinate of the king's starting position.
     */
    public King(char color, int r, int c) {
        // position.setPosition(r, c);
        position.setPosition(r, c);
        this.color = color;
    }

    /**
     * Returns the full string representation of the piece (e.g., "wK" or "bK").
     *
     * @return A string combining color and piece name.
     */
    public String getPiece() {
        return String.valueOf(color) + String.valueOf(name);
    }

    @Override
    public List<Position> possibleMoves(Piece[][] board) {
        List<Position> moves = new ArrayList<>();
        int row = position.getRow();
        int col = position.getCollumn();
        
        // King moves one square in any direction
        int[][] directions = {
            {-1, -1}, {-1, 0}, {-1, 1},  // top-left, top, top-right
            {0, -1}, {0, 1},             // left, right
            {1, -1}, {1, 0}, {1, 1}      // bottom-left, bottom, bottom-right
        };
        
        for (int[] dir : directions) {
            int newRow = row + dir[0];
            int newCol = col + dir[1];
            
            if (newRow >= 0 && newRow < 8 && newCol >= 0 && newCol < 8) {
                Piece targetPiece = board[newRow][newCol];
                if (targetPiece == null || targetPiece.getColor() != color) {
                    Position pos = new Position();
                    pos.setPosition(newRow, newCol);
                    moves.add(pos);
                }
            }
        }
        
        return moves;
    }
}
