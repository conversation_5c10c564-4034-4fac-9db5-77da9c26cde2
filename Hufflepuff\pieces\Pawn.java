package pieces; //Pawn belongs to package pieces.
import java.util.ArrayList;
import java.util.List;

/**
 * Represents a Pawn piece in a chess game.
 * Pawns move forward one square (or two on their first move) and capture diagonally.
 * They can be promoted upon reaching the opposite end of the board.
 */


public class Pawn extends Piece{

    /** The character representing this piece type. Always 'P' for Pawn. */
    private char name = 'P';

    /** The current position of the Pawn on the board. */
    //public Position position = new Position();

    /**
     * Constructs a new Pawn with the specified color and position.
     *
     * @param color The color of the pawn ('w' or 'b').
     * @param r     The row coordinate of the pawn's starting position.
     * @param c     The column coordinate of the pawn's starting position.
     */
    public Pawn(char color, int r, int c) {
        //position.setPosition(r, c);
        position.setPosition(r, c);
        this.color = color;
    }

    /**
     * Returns the full string representation of the piece (e.g., "wP" or "bP").
     *
     * @return A string combining color and piece name.
     */
    public String getPiece() {
        return String.valueOf(color) + String.valueOf(name);
    }

    @Override
    public List<Position> possibleMoves(Piece[][] board) {
        List<Position> moves = new ArrayList<>();
        int row = position.getRow();
        int col = position.getCollumn();
        
        // white moves up, black moves down)
        int direction = (color == 'w') ? -1 : 1;
        int startRow = (color == 'w') ? 1 : 6;
        
        
        // Forward 
        int newRow = row + direction;
        if (newRow >= 0 && newRow < 8 && board[newRow][col] == null) {
            Position pos = new Position();
            pos.setPosition(newRow, col);
            moves.add(pos);
            
            // Double move from starting position
            if (row == startRow) {
                newRow = row + 2 * direction;
                if (newRow >= 0 && newRow < 8 && board[newRow][col] == null) {
                    Position pos2 = new Position();
                    pos2.setPosition(newRow, col);
                    moves.add(pos2);
                }
            }
        }
        
        // captures
        int[] captureCols = {col - 1, col + 1};
        for (int captureCol : captureCols) {
            newRow = row + direction;
            if (newRow >= 0 && newRow < 8 && captureCol >= 0 && captureCol < 8) {
                Piece targetPiece = board[newRow][captureCol];
                if (targetPiece != null && targetPiece.getColor() != color) {
                    Position pos = new Position();
                    pos.setPosition(newRow, captureCol);
                    moves.add(pos);
                }
            }
        }
        
        return moves;
    }
}
