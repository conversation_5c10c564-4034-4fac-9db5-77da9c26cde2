package main; //MainClass class belongs to package main.
import board.GameBoard;
import java.util.Scanner;
/**
 * The main class to start the chess game.
 * Initializes the game board and prepares for user interaction.
 */

public class MainClass {
    /**
     * The entry point of the program.
     * Creates a new game board instance.
     *
     * @param args Command-line arguments (not used).
     */
    public static void main(String[] args) {
        Scanner in = new Scanner(System.in);
        GameBoard myGameBoard = new GameBoard();
        boolean isWhite = true;
        boolean isPlaying = true;
        boolean isLegalMove;
        while(isPlaying)
        {
            if(isWhite){
                System.out.println("It's white turn, please enter your move: ");
            }
            else{
                System.out.println("It's black turn, please enter your move: ");
            }
            String move = in.nextLine();
            isLegalMove = myGameBoard.makeMove(move, isWhite);
            if(isLegalMove){
                myGameBoard.display();
                if(isWhite){
                    isWhite =false;
                }else{
                    isWhite = true;
                }
            }
            else{
                System.out.println("The move is illegal!");
            }
        }
        in.close();
    }
}
