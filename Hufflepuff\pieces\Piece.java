package board;
import pieces.*;
import java.util.List;

public class GameBoard {
    private char[] collumn = {'A','B','C','D','E','F','G','H'};
    public Square[][] mySquare;
    public Player[] players = new Player[2];

    public GameBoard() {
        mySquare = new Square[8][8];
        players[0] = new Player('w');
        players[1] = new Player('b');
        setupBoard();
        display();
    }

    public void setupBoard() {
        // Place all pieces and their correct indices
        for (int i = 0; i < 2; i++) {
            Player p = players[i];
            
            // Pawns
            for (int j = 0; j < 8; j++) {
                mySquare[p.myPawns[j].position.getRow()][p.myPawns[j].position.getCollumn()] = new Square(p.myPawns[j].getPiece(), j);
            }

            // Rooks
            mySquare[p.myRooks[0].position.getRow()][p.myRooks[0].position.getCollumn()] = new Square(p.myRooks[0].getPiece(), 0);
            mySquare[p.myRooks[1].position.getRow()][p.myRooks[1].position.getCollumn()] = new Square(p.myRooks[1].getPiece(), 1);

            // Knights
            mySquare[p.myKnights[0].position.getRow()][p.myKnights[0].position.getCollumn()] = new Square(p.myKnights[0].getPiece(), 0);
            mySquare[p.myKnights[1].position.getRow()][p.myKnights[1].position.getCollumn()] = new Square(p.myKnights[1].getPiece(), 1);

            // Bishops
            mySquare[p.myBishops[0].position.getRow()][p.myBishops[0].position.getCollumn()] = new Square(p.myBishops[0].getPiece(), 0);
            mySquare[p.myBishops[1].position.getRow()][p.myBishops[1].position.getCollumn()] = new Square(p.myBishops[1].getPiece(), 1);

            // Queen and King
            mySquare[p.myQueen.position.getRow()][p.myQueen.position.getCollumn()] = new Square(p.myQueen.getPiece(), 0);
            mySquare[p.myKing.position.getRow()][p.myKing.position.getCollumn()] = new Square(p.myKing.getPiece(), 0);
        }
        
        // Fill empty squares
        for(int i = 0; i < 8; i++){
            for(int j = 0; j < 8; j++){
                if(mySquare[i][j] == null){
                    if((i + j) % 2 == 0){
                        mySquare[i][j] = new Square("   ", 0);
                    } else {
                        mySquare[i][j] = new Square("##", 0);
                    }
                }
            }
        }
    }

    public void display() {
        System.out.print("  ");
        for(int j = 0; j < 8; j++){
            System.out.print(collumn[j] + "  ");
        }
        System.out.print("\n");
        for(int i = 0; i < 8; i++){
            System.out.print(8 - i + " ");
            for(int j = 0; j < 8; j++){
                System.out.print(mySquare[7 - i][j].getName() + " ");
            }
            System.out.print("\n");
        }
    }

    public boolean isValidInput(String move) {
        String movePattern = "^[A-Ha-h][1-8] [A-Ha-h][1-8]$";
        return move != null && move.matches(movePattern);
    }

    public boolean makeMove(String move, boolean isWhite){
        if (!isValidInput(move)) {
            System.out.println("Invalid input format! Please enter move like 'E2 E4'");
            return false;
        }

        move = move.toUpperCase(); 
        move = decode(move);
        int turn = isWhite ? 0 : 1;
        int currC = Character.getNumericValue(move.charAt(0));
        int currR = Character.getNumericValue(move.charAt(1));
        int moveToC = Character.getNumericValue(move.charAt(2));
        int moveToR = Character.getNumericValue(move.charAt(3));
        
        if (currR < 1 || currR > 8 || currC < 0 || currC > 7 || 
            moveToR < 1 || moveToR > 8 || moveToC < 0 || moveToC > 7) {
            return false;
        }
        
        String movingPiece = mySquare[currR - 1][currC].getName();
        int index = mySquare[currR - 1][currC].getIndex();
        
        if (mySquare[currR - 1][currC] == null || movingPiece.trim().isEmpty() || movingPiece.equals("##") ||
            movingPiece.length() < 2 || movingPiece.charAt(0) != (isWhite ? 'w' : 'b')) {
            return false;
        }
        
        Piece[][] board = new Piece[8][8];
        for (int i = 0; i < 8; i++) {
            for (int j = 0; j < 8; j++) {
                if (mySquare[i][j] != null && !mySquare[i][j].getName().trim().isEmpty() && !mySquare[i][j].getName().equals("##")) {
                    char pieceColor = mySquare[i][j].getName().charAt(0);
                    char pieceType = mySquare[i][j].getName().charAt(1);
                    Piece piece = createPiece(pieceType, pieceColor, i, j);
                    if (piece != null) {
                        piece.setCurrPosition(i, j);
                    }
                    board[i][j] = piece;
                }
            }
        }
        
        Piece movingPieceObj = board[currR - 1][currC];
        if (movingPieceObj == null) return false;
        
        Position targetPos = new Position();
        targetPos.setPosition(moveToR - 1, moveToC);
        List<Position> validMoves = movingPieceObj.possibleMoves(board);
    
        boolean isValidMove = false;
        for (Position pos : validMoves) {
            if (pos.getRow() == targetPos.getRow() && pos.getCollumn() == targetPos.getCollumn()) {
                isValidMove = true;
                break;
            }
        }
        
        if (!isValidMove) {
            System.out.println("Invalid move: piece cannot move to that square.");
            return false;
        }

        Square targetSquare = mySquare[moveToR - 1][moveToC];
        String targetPiece = targetSquare.getName();

        if (!targetPiece.trim().isEmpty() && !targetPiece.equals("##")) {
            char capturedColor = targetPiece.charAt(0);
            char movingColor = movingPiece.charAt(0);

            if (capturedColor == movingColor) {
                System.out.println("Invalid move: cannot capture own piece.");
                return false;
            }

            int enemyTurn = (capturedColor == 'w') ? 0 : 1;
            int capturedIndex = targetSquare.getIndex();
            char capturedType = targetPiece.charAt(1);

            switch (capturedType) {
                case 'P': players[enemyTurn].myPawns[capturedIndex] = null; break;
                case 'R': players[enemyTurn].myRooks[capturedIndex] = null; break;
                case 'N': players[enemyTurn].myKnights[capturedIndex] = null; break;
                case 'B': players[enemyTurn].myBishops[capturedIndex] = null; break;
                case 'Q': players[enemyTurn].myQueen = null; break;
                case 'K': players[enemyTurn].myKing = null; break;
            }

            System.out.println(capturedColor + "" + capturedType + " captured!");
        }

        // Execute the move using the correct piece from the player's array
        Piece pieceToMove = null;
        char pieceType = movingPiece.charAt(1);
        switch (pieceType) {
            case 'P': pieceToMove = players[turn].myPawns[index]; break;
            case 'R': pieceToMove = players[turn].myRooks[index]; break;
            case 'N': pieceToMove = players[turn].myKnights[index]; break;
            case 'B': pieceToMove = players[turn].myBishops[index]; break;
            case 'Q': pieceToMove = players[turn].myQueen; break;
            case 'K': pieceToMove = players[turn].myKing; break;
        }

        if (pieceToMove != null) {
            pieceToMove.position.setPosition(moveToR - 1, moveToC);
            mySquare[moveToR - 1][moveToC] = new Square(movingPiece, index);
            if (((currR - 1) + currC) % 2 == 0) {
                mySquare[currR - 1][currC] = new Square("   ", 0);
            } else {
                mySquare[currR - 1][currC] = new Square("##", 0);
            }
            return true;
        }

        return false;
    }
    
    private Piece createPiece(char pieceType, char color, int row, int col) {
        switch (pieceType) {
            case 'P': return new Pawn(color, row, col);
            case 'R': return new Rook(color, row, col);
            case 'N': return new Knight(color, row, col);
            case 'B': return new Bishop(color, row, col);
            case 'Q': return new Queen(color, row, col);
            case 'K': return new King(color, row, col);
            default: return null;
        }
    }

    public String decode(String move) {
        int srcCol = move.charAt(0) - 'A';
        int srcRow = Character.getNumericValue(move.charAt(1));
        int destCol = move.charAt(3) - 'A';
        int destRow = Character.getNumericValue(move.charAt(4));
        return String.valueOf(srcCol) + srcRow + destCol + destRow;
    }
}