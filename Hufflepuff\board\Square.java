package board;

import pieces.Piece;

/**
 * Represents a single square on the chessboard.
 * Each square can either hold a chess piece or be empty.
 * It stores a name for display purposes (e.g., "wP", "##") and a direct
 * reference to the Piece object occupying it.
 */
public class Square {

    /**
     * The name of the piece on this square for display, or "   " / "##" for empty squares.
     */
    private String name;

    /**
     * A reference to the Piece object on this square. Null if the square is empty.
     */
    private Piece piece;

    /**
     * Constructs a new Square with a given display name and a Piece object.
     *
     * @param name  The display name of the piece (e.g., "wP", "bR").
     * @param piece The Piece object on this square, or null if the square is empty.
     */
    public Square(String name, Piece piece) {
        this.name = name;
        this.piece = piece;
    }

    /**
     * Returns the display name of the piece on this square.
     *
     * @return The name string.
     */
    public String getName() {
        return name;
    }

    /**
     * Returns the Piece object on this square.
     *
     * @return The Piece object, or null if the square is empty.
     */
    public Piece getPiece() {
        return piece;
    }
}