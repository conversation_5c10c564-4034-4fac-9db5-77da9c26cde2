package pieces; //Knight belongs to package pieces.
import java.util.ArrayList;
import java.util.List;

/**
 * Represents a Knight piece in a chess game.
 * A Knight moves in an L-shape: two squares in one direction,
 * then one square perpendicular to that. Knights can jump over other pieces.
 */

public class Knight extends Piece {

    /** The character representing this piece type. Always 'N' for Knight. */
    private char name = 'N';

    /** The current position of the Knight on the board. */
    //public Position position = new Position();

    /**
     * Constructs a new Knight with the specified color and position.
     *
     * @param color The color of the knight ('w' or 'b').
     * @param r     The row coordinate of the knight's starting position.
     * @param c     The column coordinate of the knight's starting position.
     */
    public Knight(char color, int r, int c) {
        //position.setPosition(r, c);
        position.setPosition(r, c);
        this.color = color;
    }

    /**
     * Returns the full string representation of the piece (e.g., "wN" or "bN").
     *
     * @return A string combining color and piece name.
     */
    public String getPiece() {
        return String.valueOf(color) + String.valueOf(name);
    }
     
    @Override
    public List<Position> possibleMoves(Piece[][] board) {
        List<Position> moves = new ArrayList<>();
        int row = position.getRow();
        int col = position.getCollumn();
        
        // L-shape
        int[][] knightMoves = {
            {-2, -1}, {-2, 1}, {-1, -2}, {-1, 2},
            {1, -2}, {1, 2}, {2, -1}, {2, 1}
        };
        
        for (int[] move : knightMoves) {
            int newRow = row + move[0];
            int newCol = col + move[1];
            
            if (newRow >= 0 && newRow < 8 && newCol >= 0 && newCol < 8) {
                Piece targetPiece = board[newRow][newCol];
                if (targetPiece == null || targetPiece.getColor() != color) {
                    Position pos = new Position();
                    pos.setPosition(newRow, newCol);
                    moves.add(pos);
                }
            }
        }
        
        return moves;
    }
}
