package pieces; //Rook belongs to package pieces.
import java.util.ArrayList;
import java.util.List;

/**
 * Represents a Rook piece in a chess game.
 * A Rook moves horizontally or vertically any number of squares.
 * It also plays a role in castling with the King.
 */



public class Rook extends Piece {

    /** The character representing this piece type. Always 'R' for Rook. */
    private char name = 'R';

    /** The current position of the Rook on the board. */
    //public Position position = new Position();

    /**
     * Constructs a new Rook with the specified color and position.
     *
     * @param color The color of the rook ('w' or 'b').
     * @param r     The row coordinate of the rook's starting position.
     * @param c     The column coordinate of the rook's starting position.
     */
    public Rook(char color, int r, int c) {
        //position.setPosition(r, c);
        position.setPosition(r, c);
        this.color = color;
    }

    /**
     * Returns the full string representation of the piece (e.g., "wR" or "bR").
     *
     * @return A string combining color and piece name.
     */
    public String getPiece() {
        return String.valueOf(color) + String.valueOf(name);
    }


    @Override
    public List<Position> possibleMoves(Piece[][] board) {
        List<Position> moves = new ArrayList<>();
        int row = position.getRow();
        int col = position.getCollumn();
        
        // Horizontal moves (left and right)
        int[] directions = {-1, 1};
        
        // Check left and right
        for (int dir : directions) {
            int newCol = col + dir;
            while (newCol >= 0 && newCol < 8) {
                Piece targetPiece = board[row][newCol];
                if (targetPiece == null) {
                    Position pos = new Position();
                    pos.setPosition(row, newCol);
                    moves.add(pos);
                } else {
                    if (targetPiece.getColor() != color) {
                        Position pos = new Position();
                        pos.setPosition(row, newCol);
                        moves.add(pos);
                    }
                    break; // Stop at first piece
                }
                newCol += dir;
            }
        }
        
        // Check up and down
        for (int dir : directions) {
            int newRow = row + dir;
            while (newRow >= 0 && newRow < 8) {
                Piece targetPiece = board[newRow][col];
                if (targetPiece == null) {
                    Position pos = new Position();
                    pos.setPosition(newRow, col);
                    moves.add(pos);
                } else {
                    if (targetPiece.getColor() != color) {
                        Position pos = new Position();
                        pos.setPosition(newRow, col);
                        moves.add(pos);
                    }
                    break; // Stop at first piece
                }
                newRow += dir;
            }
        }
        
        return moves;
    }
}
