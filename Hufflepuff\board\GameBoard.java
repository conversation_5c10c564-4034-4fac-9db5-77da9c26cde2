package board;

import pieces.*;
import java.util.List;

/**
 * Represents the chessboard and manages the game state.
 * This class is responsible for initializing the board, placing pieces,
 * displaying the board, and handling the logic for move validation and execution.
 */
public class GameBoard {

    /**
     * The column labels for the chessboard (A through H).
     */
    private char[] collumn = {'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H'};

    /**
     * A 2D array of Squares representing the chessboard grid.
     */
    public Square[][] mySquare;

    /**
     * An array holding the white and black player's pieces.
     */
    public Player[] players = new Player[2];

    /**
     * Constructs a new GameBoard.
     * Initializes the squares, creates the players, and sets up the pieces
     * in their starting positions. The board is then immediately displayed.
     */
    public GameBoard() {
        mySquare = new Square[8][8];
        players[0] = new Player('w');
        players[1] = new Player('b');
        setupBoard();
        display();
    }

    /**
     * Sets up the chessboard by placing all pieces in their starting positions.
     * This method iterates through both players' piece arrays and places
     * each piece at its initial coordinates on the board. Empty squares
     * are filled with alternating display names ("   " and "##").
     */
    public void setupBoard() {
        // Place all pieces
        for (int i = 0; i < 2; i++) {
            Player p = players[i];
            
            // Pawns
            for (int j = 0; j < 8; j++) {
                Piece pawn = p.myPawns[j];
                mySquare[pawn.position.getRow()][pawn.position.getCollumn()] = new Square(pawn.getPiece(), pawn);
            }

            // Rooks, Knights, Bishops
            mySquare[p.myRooks[0].position.getRow()][p.myRooks[0].position.getCollumn()] = new Square(p.myRooks[0].getPiece(), p.myRooks[0]);
            mySquare[p.myRooks[1].position.getRow()][p.myRooks[1].position.getCollumn()] = new Square(p.myRooks[1].getPiece(), p.myRooks[1]);
            mySquare[p.myKnights[0].position.getRow()][p.myKnights[0].position.getCollumn()] = new Square(p.myKnights[0].getPiece(), p.myKnights[0]);
            mySquare[p.myKnights[1].position.getRow()][p.myKnights[1].position.getCollumn()] = new Square(p.myKnights[1].getPiece(), p.myKnights[1]);
            mySquare[p.myBishops[0].position.getRow()][p.myBishops[0].position.getCollumn()] = new Square(p.myBishops[0].getPiece(), p.myBishops[0]);
            mySquare[p.myBishops[1].position.getRow()][p.myBishops[1].position.getCollumn()] = new Square(p.myBishops[1].getPiece(), p.myBishops[1]);

            // Queen and King
            mySquare[p.myQueen.position.getRow()][p.myQueen.position.getCollumn()] = new Square(p.myQueen.getPiece(), p.myQueen);
            mySquare[p.myKing.position.getRow()][p.myKing.position.getCollumn()] = new Square(p.myKing.getPiece(), p.myKing);
        }
        
        // Fill empty squares with alternating colors (e.g., "   " and "##")
        for (int i = 0; i < 8; i++) {
            for (int j = 0; j < 8; j++) {
                if (mySquare[i][j] == null) {
                    if ((i + j) % 2 == 0) {
                        mySquare[i][j] = new Square("   ", null); // Pass null for empty squares
                    } else {
                        mySquare[i][j] = new Square("##", null); // Pass null for empty squares
                    }
                }
            }
        }
    }

    /**
     * Displays the current state of the chessboard in the console.
     * The board is printed with row numbers (1-8) and column letters (A-H).
     */
    public void display() {
        System.out.print("  ");
        for (int j = 0; j < 8; j++) {
            System.out.print(collumn[j] + "  ");
        }
        System.out.print("\n");
        for (int i = 0; i < 8; i++) {
            System.out.print(8 - i + " ");
            for (int j = 0; j < 8; j++) {
                System.out.print(mySquare[7 - i][j].getName() + " ");
            }
            System.out.print("\n");
        }
    }
    
    /**
     * Validates the format of a move input string using a regular expression.
     *
     * @param move The move string (e.g., "A2 A3").
     * @return True if the format is valid, false otherwise.
     */
    public boolean isValidInput(String move) {
        String movePattern = "^[A-Ha-h][1-8] [A-Ha-h][1-8]$";
        return move != null && move.matches(movePattern);
    }

    /**
     * Makes a move on the board after validating it.
     * This method first checks if the input format is correct, then validates the
     * move's legality based on the piece's movement rules. If the move is valid,
     * it updates the board state.
     *
     * @param move The move string in standard chess notation (e.g., "A2 A3").
     * @param isWhite True if it's white's turn, false if black's.
     * @return True if the move is legal and executed, false otherwise.
     */
    public boolean makeMove(String move, boolean isWhite) {
        // --- FIX: The isValidInput check is the first line of defense against exceptions.
        if (!isValidInput(move)) {
            System.out.println("Invalid input format! Please enter move like 'E2 E4'");
            return false;
        }

        // --- FIX: Convert the entire string to uppercase to handle 'a2 a3' correctly.
        String originalMove = move;
        move = decode(move.toUpperCase());
        System.out.println("DEBUG: Original move: " + originalMove);
        System.out.println("DEBUG: After decode: " + move);
        int turn = isWhite ? 0 : 1;
        int currC = Character.getNumericValue(move.charAt(0));
        int currR = Character.getNumericValue(move.charAt(1));
        int moveToC = Character.getNumericValue(move.charAt(2));
        int moveToR = Character.getNumericValue(move.charAt(3));
        System.out.println("DEBUG: currC=" + currC + ", currR=" + currR + ", moveToC=" + moveToC + ", moveToR=" + moveToR);
        System.out.println("DEBUG: Board position [" + (currR-1) + "][" + currC + "] to [" + (moveToR-1) + "][" + moveToC + "]");
        
        // Check if source and destination positions are within board bounds
        if (currR < 1 || currR > 8 || currC < 0 || currC > 7 || 
            moveToR < 1 || moveToR > 8 || moveToC < 0 || moveToC > 7) {
            return false;
        }

        // Get the actual piece object from the board
        Piece movingPieceObj = mySquare[currR - 1][currC].getPiece();
        if (movingPieceObj == null || movingPieceObj.getColor() != (isWhite ? 'w' : 'b')) {
            System.out.println("No piece at source or it doesn't belong to you.");
            return false;
        }
        
        // Create a board representation for move validation
        Piece[][] board = new Piece[8][8];
        for (int i = 0; i < 8; i++) {
            for (int j = 0; j < 8; j++) {
                board[i][j] = mySquare[i][j].getPiece();
            }
        }

        // Check if the move is a valid one for the piece
        Position targetPos = new Position();
        targetPos.setPosition(moveToR - 1, moveToC);
        List<Position> validMoves = movingPieceObj.possibleMoves(board);
    
        boolean isValidMove = false;
        for (Position pos : validMoves) {
            if (pos.getRow() == targetPos.getRow() && pos.getCollumn() == targetPos.getCollumn()) {
                isValidMove = true;
                break;
            }
        }
        
        if (!isValidMove) {
            System.out.println("Invalid move: piece cannot move to that square.");
            return false;
        }

        // Handle capture if there's a piece at the destination
        Piece capturedPieceObj = mySquare[moveToR - 1][moveToC].getPiece();
        if (capturedPieceObj != null) {
            System.out.println(capturedPieceObj.getPiece() + " captured!");
        }
        
        // Execute the move: update the piece's position and the board squares
        movingPieceObj.position.setPosition(moveToR - 1, moveToC);
        mySquare[moveToR - 1][moveToC] = new Square(movingPieceObj.getPiece(), movingPieceObj);
        
        // Set the old square to empty
        if (((currR - 1) + currC) % 2 == 0) {
            mySquare[currR - 1][currC] = new Square("   ", null);
        } else {
            mySquare[currR - 1][currC] = new Square("##", null);
        }
        
        return true;
    }
    
    /**
     * Decodes a move string in algebraic notation into internal coordinate
     * representation.
     *
     * @param move The move string (e.g., "A2 A3").
     * @return A decoded string representing the move coordinates (e.g., "0102").
     */
    public String decode(String move) {
        // Expecting format: A2 A3
        int srcCol = move.charAt(0) - 'A';
        int srcRow = Character.getNumericValue(move.charAt(1));
        int destCol = move.charAt(3) - 'A';
        int destRow = Character.getNumericValue(move.charAt(4));

        return String.valueOf(srcCol) + srcRow + destCol + destRow;
    }
}