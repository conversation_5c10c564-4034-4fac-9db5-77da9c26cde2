package test;
import main.MainClass;
import pieces.Pieces;
import pieces.Position;

/**
 * A test class used to verify the creation and output of chess pieces.
 * This class initializes a set of white pieces and prints their information.
 */

public class Test {

    /**
     * The main method to run the test.
     * It creates a white player's set of pieces and prints their types and positions.
     *
     * @param args Command-line arguments (not used).
     */
    public static void main(String[] args) {
        Pieces wPieces = new Pieces('w');
        System.out.println(wPieces.myRooks[0].getPiece());
        System.out.println(wPieces.myRooks[0].getCurrPosition());
        System.out.println(wPieces.myKnights[0].getPiece());
        System.out.println(wPieces.myKnights[0].getCurrPosition());
        System.out.println(wPieces.myQueen.getPiece());
        System.out.println(wPieces.myQueen.getCurrPosition());
    }
}
